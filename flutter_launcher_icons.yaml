flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/logo.jpg"
  min_sdk_android: 21

  # Configuration pour icônes adaptatives circulaires
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/logo.jpg"

  # Configuration pour forcer l'affichage circulaire
  adaptive_icon_monochrome: "assets/logo.jpg"

  # Configuration Web avec préservation de forme
  web:
    generate: true
    image_path: "assets/logo.jpg"
    background_color: "#ffffff"
    theme_color: "#ffffff"

  # Configuration Windows avec taille optimale
  windows:
    generate: true
    image_path: "assets/logo.jpg"
    icon_size: 256 # Taille maximale pour meilleure qualité

  # Configuration macOS
  macos:
    generate: true
    image_path: "assets/logo.jpg"

  # Configuration Linux
  linux:
    generate: true
    image_path: "assets/logo.jpg"

  # Préservation de la forme originale pour iOS
  remove_alpha_ios: false
