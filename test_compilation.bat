@echo off
echo Fixing Flutter dependency issues...
echo.

echo Step 1: Cleaning project...
flutter clean
if %errorlevel% neq 0 (
    echo ERROR: Flutter clean failed
    pause
    exit /b 1
)

echo.
echo Step 2: Removing pubspec.lock...
if exist pubspec.lock del pubspec.lock

echo.
echo Step 3: Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Flutter pub get failed
    pause
    exit /b 1
)

echo.
echo Step 4: Upgrading dependencies...
flutter pub upgrade
if %errorlevel% neq 0 (
    echo WARNING: Flutter pub upgrade had issues, continuing...
)

echo.
echo Step 5: Testing compilation (dry run)...
flutter build apk --debug --dry-run
if %errorlevel% neq 0 (
    echo ERROR: Flutter build failed
    echo.
    echo This might be due to incompatible dependencies.
    echo Try running: flutter pub deps
    pause
    exit /b 1
)

echo.
echo SUCCESS: All tests passed!
echo Dependencies have been updated and the build works.
echo You can now run: flutter run
echo.
pause
