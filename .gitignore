# Flutter/Dart build artifacts
build/
*.aar
*.dex
*.class
*.jar
*.bin
*.lock
.transforms/
intermediates/
tmp/
outputs/
generated/

# Flutter/Dart specific
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
flutter_*.log
*.iml
*.idea/
*.ipr
*.iws

# iOS
ios/Pods/
ios/Podfile.lock
ios/.symlinks/
ios/Flutter/flutter_export_environment.sh
ios/Flutter/Generated.xcconfig
ios/Flutter/app.flutter_plugin
ios/Flutter/flutter_export_environment.sh
ios/Runner.xcworkspace/xcuserdata/
ios/Runner.xcodeproj/xcuserdata/

# macOS
macos/Flutter/ephemeral/
macos/Flutter/Flutter-Generated.xcconfig
macos/Runner.xcworkspace/xcuserdata/
macos/Runner.xcodeproj/xcuserdata/

# Windows
windows/flutter/ephemeral/
windows/runner/Debug/
windows/runner/Release/
windows/runner/Profile/

# Linux
linux/flutter/ephemeral/

# Web
web/build/

# System files
ntuser*
NTUSER*
*.log
*.dat
*.blf
*.regtrans-ms

# Personal directories
AppData/
Documents/
Downloads/
Pictures/
Videos/
Music/
OneDrive/
Favorites/
Searches/
Saved Games/
3D Objects/
Contacts/
Links/
WPS Cloud Files/

# Development tools
.android/
.bito/
.cache/
.codegpt/
.config/
.cursor/
.dbtcode/
.eclipse/
.gradle/
.m2/
.matplotlib/
.nuget/
.p2/
.ssh/
.vscode/
eclipse-workspace/
eclipse/
genymotion-logs-*.zip
node_modules/
package-lock.json

# Temporary and personal files
*.lnk
*.jfif
*.png
*.pdf
desktop.ini
