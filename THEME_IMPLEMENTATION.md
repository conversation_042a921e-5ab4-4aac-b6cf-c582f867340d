# Implémentation du Mode Dark/Light

## Vue d'ensemble

Cette implémentation ajoute le support complet du mode dark et light à l'application Flutter. Le système est conçu pour être flexible, maintenable et facile à utiliser.

## Fonctionnalités

### 1. **Modes de thème supportés**
- **Mode Clair** : Thème traditionnel avec fond blanc et texte sombre
- **Mode Sombre** : Thème sombre avec fond noir et texte clair
- **Mode Système** : Suit automatiquement les préférences système de l'utilisateur

### 2. **Persistance des préférences**
- Les préférences de thème sont sauvegardées localement avec `SharedPreferences`
- L'application se souvient du choix de l'utilisateur entre les sessions

### 3. **Interface utilisateur**
- Contrôle du thème intégré dans l'écran des paramètres
- Interface intuitive avec icônes et sélection visuelle
- Support multilingue (Arabe/Français)

## Architecture

### Fichiers créés/modifiés

#### **Nouveaux fichiers :**
1. `lib/src/config/theme_provider.dart` - Gestion d'état du thème
2. `lib/src/config/app_themes.dart` - Définition des thèmes Flutter
3. `lib/src/config/theme_helper.dart` - Utilitaires pour les couleurs du thème

#### **Fichiers modifiés :**
1. `lib/src/config/colors.dart` - Palettes de couleurs étendues
2. `lib/main.dart` - Intégration du ThemeProvider
3. `lib/src/widgets/settings.dart` - Interface de contrôle du thème
4. `lib/src/widgets/signin.dart` - Exemple d'utilisation des couleurs du thème
5. `pubspec.yaml` - Ajout de la dépendance `shared_preferences`

## Utilisation

### 1. **Obtenir les couleurs du thème actuel**

```dart
// Dans un widget
final colors = ThemeHelper.getColorsWithListener(context);

// Utiliser les couleurs
Container(
  color: colors.backgroundPrimary,
  child: Text(
    'Hello World',
    style: TextStyle(color: colors.textPrimary),
  ),
)
```

### 2. **Utiliser les styles prédéfinis**

```dart
// Styles de texte
Text(
  'Titre',
  style: ThemeHelper.getTitleStyle(context),
)

Text(
  'Sous-titre',
  style: ThemeHelper.getSubtitleStyle(context),
)

// Décoration d'input
TextFormField(
  decoration: ThemeHelper.getInputDecoration(
    context,
    labelText: 'Email',
    hintText: '<EMAIL>',
  ),
)

// Style de bouton
ElevatedButton(
  style: ThemeHelper.getPrimaryButtonStyle(context),
  child: Text(
    'Bouton',
    style: ThemeHelper.getButtonTextStyle(context),
  ),
)
```

### 3. **Changer le thème**

```dart
// Obtenir le provider
final themeProvider = Provider.of<ThemeProvider>(context, listen: false);

// Changer le mode
themeProvider.setThemeMode(AppThemeMode.dark);   // Mode sombre
themeProvider.setThemeMode(AppThemeMode.light);  // Mode clair
themeProvider.setThemeMode(AppThemeMode.system); // Mode système
```

## Couleurs disponibles

### Mode Clair (Light)
- `backgroundPrimary` : Blanc pur
- `backgroundSecondary` : Gris très clair
- `textPrimary` : Noir
- `textSecondary` : Gris foncé
- `card` : Blanc
- `surface` : Blanc

### Mode Sombre (Dark)
- `backgroundPrimary` : Noir profond (#121212)
- `backgroundSecondary` : Gris très sombre (#1E1E1E)
- `textPrimary` : Blanc cassé (#E0E0E0)
- `textSecondary` : Gris clair (#B0B0B0)
- `card` : Gris sombre (#2D2D2D)
- `surface` : Gris sombre (#1E1E1E)

### Couleurs communes
- `textAccent` : Orange principal (#FF5722)
- `buttonPrimary` : Orange principal
- `borderFocused` : Orange principal
- `borderPrimary` : Adapté au thème

## Migration des écrans existants

Pour migrer un écran existant vers le nouveau système de thème :

1. **Remplacer les couleurs statiques :**
```dart
// Avant
backgroundColor: AppColors.lightGray,

// Après
backgroundColor: ThemeHelper.getColors(context).backgroundSecondary,
```

2. **Utiliser les helpers pour les styles :**
```dart
// Avant
style: Constants.titleStyle,

// Après
style: ThemeHelper.getTitleStyle(context),
```

3. **Ajouter le listener si nécessaire :**
```dart
// Pour les widgets qui doivent se reconstruire lors du changement de thème
final colors = ThemeHelper.getColorsWithListener(context);
```

## Bonnes pratiques

1. **Utiliser les helpers** : Préférer `ThemeHelper` aux couleurs directes
2. **Listener approprié** : Utiliser `getColorsWithListener` seulement quand nécessaire
3. **Cohérence** : Maintenir la cohérence visuelle entre les modes
4. **Test** : Tester l'application dans les deux modes
5. **Accessibilité** : S'assurer que les contrastes sont suffisants

## Tests

Pour tester l'implémentation :

1. Lancer l'application
2. Aller dans Paramètres
3. Changer le mode de thème
4. Vérifier que tous les écrans s'adaptent correctement
5. Redémarrer l'application pour vérifier la persistance

## Prochaines étapes

1. Migrer tous les écrans restants vers le nouveau système
2. Ajouter des animations de transition entre les thèmes
3. Implémenter des thèmes personnalisés
4. Ajouter plus de variantes de couleurs si nécessaire
