import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';
import '../config/colors.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import 'locale_provider.dart';

class AppealScreen extends StatefulWidget {
  final String requestId;
  final String requestType;

  const AppealScreen({
    Key? key,
    required this.requestId,
    required this.requestType,
  }) : super(key: key);

  @override
  State<AppealScreen> createState() => _AppealScreenState();
}

class _AppealScreenState extends State<AppealScreen> {
  final _reasonController = TextEditingController();
  final _detailsController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isSubmitting = false;
  final _storage = const FlutterSecureStorage();

  @override
  void dispose() {
    _reasonController.dispose();
    _detailsController.dispose();
    super.dispose();
  }

  Future<String?> _getAuthToken() async {
    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null) {
        print('No auth token found in FlutterSecureStorage');
      } else {
        print('Retrieved auth token: $token');
      }
      return token;
    } catch (e) {
      print('Error reading auth token from FlutterSecureStorage: $e');
      return null;
    }
  }

  Future<void> _submitAppeal() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSubmitting = true;
      });
      print('Starting appeal submission for request ID: ${widget.requestId}');
      print('Appeal content: ${_detailsController.text}');

      try {
        // Retrieve the auth token from FlutterSecureStorage
        final authToken = await _getAuthToken();

        if (authToken == null) {
          print('No auth token available');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                AppLocalizations.tr(context, 'auth_token_missing') ??
                    'Authentication token is missing',
              ),
              backgroundColor: AppColors.error,
              action: SnackBarAction(
                label: AppLocalizations.tr(context, 'login') ?? 'Login',
                textColor: AppColors.pureWhite,
                onPressed: () {
                  // Navigate to login screen using a safer approach
                  try {
                    Navigator.pushNamed(context, '/login');
                  } catch (e) {
                    print('Navigation to login failed: $e');
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          AppLocalizations.tr(context, 'navigation_error') ??
                              'Navigation to login failed. Please try again.',
                        ),
                        backgroundColor: AppColors.error,
                      ),
                    );
                  }
                },
              ),
            ),
          );
          setState(() {
            _isSubmitting = false;
          });
          return;
        }

        final url = Uri.parse(
            'https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/requests/${widget.requestId}/appeals');
        print('Sending POST request to: $url');

        final response = await http.post(
          url,
          headers: {
            'Accept': 'application/json',
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'application/json',
          },
          body: jsonEncode({
            'content': _detailsController.text,
          }),
        );

        print('Response status code: ${response.statusCode}');
        print('Response body: ${response.body}');

        if (response.statusCode == 200 || response.statusCode == 201) {
          print('Appeal submitted successfully');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                AppLocalizations.tr(context, 'appeal_submitted_success') ??
                    'Appeal submitted successfully',
              ),
              backgroundColor: AppColors.success,
            ),
          );
          Navigator.pop(context);
        } else {
          print('Appeal submission failed with status: ${response.statusCode}');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                AppLocalizations.tr(
                      context,
                      response.statusCode == 403
                          ? 'appeal_unauthorized'
                          : 'appeal_submission_failed',
                    ) ??
                    'Failed to submit appeal',
              ),
              backgroundColor: AppColors.error,
            ),
          );
        }
      } catch (e) {
        print('Error during appeal submission: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.tr(context, 'appeal_submission_error') ??
                  'An error occurred while submitting the appeal',
            ),
            backgroundColor: AppColors.error,
          ),
        );
      } finally {
        setState(() {
          _isSubmitting = false;
        });
        print('Appeal submission process completed');
      }
    } else {
      print('Form validation failed');
    }
  }

  @override
  Widget build(BuildContext context) {
    final colors = ThemeHelper.getColorsWithListener(context);
    print('Building AppealScreen for request ID: ${widget.requestId}');

    return Scaffold(
      backgroundColor: colors.backgroundPrimary,
      appBar: AppBar(
        title: Text(
          AppLocalizations.tr(context, 'submit_appeal') ?? 'Submit Appeal',
          style: ThemeHelper.getTitleStyle(context),
        ),
        backgroundColor: AppColors.error,
        foregroundColor: AppColors.pureWhite,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                elevation: 2,
                color: colors.card,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppLocalizations.tr(context, 'request_info') ??
                            'Request Information',
                        style: ThemeHelper.getSectionTitleStyle(context),
                      ),
                      const SizedBox(height: 16),
                      _buildInfoRow(
                        AppLocalizations.tr(context, 'request_number_label') ??
                            'Request Number',
                        widget.requestId,
                      ),
                      const SizedBox(height: 8),
                      _buildInfoRow(
                        AppLocalizations.tr(context, 'request_type_label') ??
                            'Request Type',
                        widget.requestType,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Card(
                elevation: 2,
                color: colors.card,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppLocalizations.tr(context, 'appeal_details') ??
                            'Appeal Details',
                        style: ThemeHelper.getSectionTitleStyle(context),
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _reasonController,
                        decoration: ThemeHelper.getInputDecoration(
                          context,
                          labelText: AppLocalizations.tr(context, 'appeal_reason') ??
                              'Appeal Reason',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            print('Appeal reason validation failed');
                            return AppLocalizations.tr(context, 'enter_appeal_reason') ??
                                'Please enter the appeal reason';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _detailsController,
                        decoration: ThemeHelper.getInputDecoration(
                          context,
                          labelText: AppLocalizations.tr(context, 'additional_details') ??
                              'Additional Details',
                        ),
                        maxLines: 5,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            print('Additional details validation failed');
                            return AppLocalizations.tr(context, 'enter_additional_details') ??
                                'Please enter additional details';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isSubmitting ? null : _submitAppeal,
                  style: ThemeHelper.getPrimaryButtonStyle(context).copyWith(
                    backgroundColor: WidgetStateProperty.all(AppColors.error),
                    padding:
                        WidgetStateProperty.all(const EdgeInsets.symmetric(vertical: 16)),
                  ),
                  child: _isSubmitting
                      ? const CircularProgressIndicator(
                          color: AppColors.pureWhite,
                        )
                      : Text(
                          AppLocalizations.tr(context, 'submit_appeal') ?? 'Submit Appeal',
                          style:
                              ThemeHelper.getButtonTextStyle(context).copyWith(fontSize: 16),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: ThemeHelper.getSubtitleStyle(context).copyWith(
            color: ThemeHelper.getColors(context).textSecondary,
          ),
        ),
        Text(
          value,
          style: ThemeHelper.getSubtitleStyle(context).copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}