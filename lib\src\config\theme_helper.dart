import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'colors.dart';
import 'theme_provider.dart';

class ThemeHelper {
  // Get colors without listener (for static usage)
  static dynamic getColors(BuildContext context) {
    try {
      final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
      return themeProvider.isDarkMode ? AppColors.dark : AppColors.light;
    } catch (e) {
      // Fallback to light theme if provider is not available
      return AppColors.light;
    }
  }

  // Get colors with listener (for widgets that need to rebuild on theme change)
  static dynamic getColorsWithListener(BuildContext context) {
    try {
      final themeProvider = Provider.of<ThemeProvider>(context, listen: true);
      return themeProvider.isDarkMode ? AppColors.dark : AppColors.light;
    } catch (e) {
      // Fallback to light theme if provider is not available
      return AppColors.light;
    }
  }

  // Text Styles
  static TextStyle getTitleStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 32,
      fontWeight: FontWeight.bold,
      color: colors.textPrimary,
      height: 1.2,
    );
  }

  static TextStyle getSectionTitleStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.bold,
      color: colors.textPrimary,
      height: 1.3,
    );
  }

  static TextStyle getSubtitleStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 18,
      color: colors.textSecondary,
      height: 1.5,
    );
  }

  static TextStyle getBodyStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.normal,
      color: colors.textPrimary,
    );
  }

  static TextStyle getButtonTextStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.bold,
      color: colors.textOnPrimary,
      letterSpacing: 0.5,
    );
  }

  // Button Styles
  static ButtonStyle getPrimaryButtonStyle(BuildContext context) {
    final colors = getColors(context);
    return ElevatedButton.styleFrom(
      backgroundColor: colors.buttonPrimary,
      foregroundColor: colors.textOnPrimary,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 2,
    );
  }

  static ButtonStyle getSecondaryButtonStyle(BuildContext context) {
    final colors = getColors(context);
    return ElevatedButton.styleFrom(
      backgroundColor: colors.buttonSecondary,
      foregroundColor: colors.textPrimary,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: colors.borderPrimary),
      ),
      elevation: 1,
    );
  }

  // Input Decoration
  static InputDecoration getInputDecoration(
    BuildContext context, {
    required String labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    final colors = getColors(context);
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      labelStyle: TextStyle(color: colors.textSecondary),
      hintStyle: TextStyle(color: colors.textSecondary),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colors.borderPrimary, width: 1.5),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colors.borderPrimary, width: 1.5),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colors.borderFocused, width: 3.0),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colors.borderError, width: 1.5),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colors.borderError, width: 3.0),
      ),
      filled: true,
      fillColor: colors.surface,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    );
  }

  // Card Theme
  static CardTheme getCardTheme(BuildContext context) {
    final colors = getColors(context);
    return CardTheme(
      color: colors.card,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }

  // App Bar Theme
  static AppBarTheme getAppBarTheme(BuildContext context) {
    final colors = getColors(context);
    return AppBarTheme(
      backgroundColor: colors.backgroundSecondary,
      foregroundColor: colors.textPrimary,
      elevation: 0,
      titleTextStyle: getSectionTitleStyle(context),
      iconTheme: IconThemeData(color: colors.textPrimary),
    );
  }

  // Bottom Navigation Bar Theme
  static BottomNavigationBarThemeData getBottomNavTheme(BuildContext context) {
    final colors = getColors(context);
    return BottomNavigationBarThemeData(
      backgroundColor: colors.surface,
      selectedItemColor: AppColors.primaryOrange,
      unselectedItemColor: colors.textSecondary,
      type: BottomNavigationBarType.fixed,
    );
  }
}
