@echo off
echo Testing Android Gradle Plugin fix...
echo.

echo Step 1: Cleaning project...
flutter clean
if %errorlevel% neq 0 (
    echo ERROR: Flutter clean failed
    pause
    exit /b 1
)

echo.
echo Step 2: Removing Gradle cache...
if exist android\.gradle rmdir /s /q android\.gradle 2>nul
if exist %USERPROFILE%\.gradle\caches rmdir /s /q %USERPROFILE%\.gradle\caches 2>nul

echo.
echo Step 3: Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Flutter pub get failed
    pause
    exit /b 1
)

echo.
echo Step 4: Testing build with new AGP version...
flutter build apk --debug --dry-run
if %errorlevel% neq 0 (
    echo ERROR: Build still failing
    echo.
    echo Additional troubleshooting:
    echo 1. Check Java version: java -version
    echo 2. Update Flutter: flutter upgrade
    echo 3. Check Android SDK: flutter doctor
    echo 4. Try: flutter doctor --android-licenses
    pause
    exit /b 1
)

echo.
echo SUCCESS: Android Gradle Plugin fix worked!
echo AGP version updated from 8.1.0 to 8.2.2
echo Kotlin version updated from 1.8.22 to 1.9.10
echo.
echo You can now run: flutter run
echo.
pause
