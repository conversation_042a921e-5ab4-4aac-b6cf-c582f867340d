import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import '../widgets/locale_provider.dart';
import '../widgets/dashboard.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  @override
  _SignUpScreenState createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _fullNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final _storage = const FlutterSecureStorage();
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  String? _validateName(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.tr(context, 'enter_name');
    }
    if (value.length < 3) {
      return AppLocalizations.tr(context, 'name_too_short');
    }
    return null;
  }

  String? _validateEmail(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.tr(context, 'enter_email');
    }
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
    if (!emailRegex.hasMatch(value)) {
      return AppLocalizations.tr(context, 'invalid_email');
    }
    return null;
  }

  String? _validatePhone(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.tr(context, 'enter_phone');
    }
    final phoneRegex = RegExp(r'^\+?[\d\s-]{8,}$');
    if (!phoneRegex.hasMatch(value)) {
      return AppLocalizations.tr(context, 'invalid_phone');
    }
    return null;
  }

  String? _validatePassword(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.tr(context, 'enter_password');
    }
    if (value.length < 8) {
      return AppLocalizations.tr(context, 'password_min_length');
    }
    return null;
  }

  String? _validateConfirmPassword(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.tr(context, 'confirm_password_prompt');
    }
    if (value != _passwordController.text) {
      return AppLocalizations.tr(context, 'passwords_not_match');
    }
    return null;
  }

  String? _validateAddress(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.tr(context, 'enter_address');
    }
    return null;
  }

  Future<void> _handleSignup() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final url = Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/register');
      final headers = {'Content-Type': 'application/json'};
      final body = jsonEncode({
        'name': _fullNameController.text.trim(),
        'email': _emailController.text.trim(),
        'password': _passwordController.text,
        'password_confirmation': _confirmPasswordController.text,
        'address': _addressController.text.trim(),
        'phoneNumber': _phoneController.text.trim(),
      });

      try {
        final response = await http.post(url, headers: headers, body: body);

        print('API Response: ${response.statusCode} - ${response.body}');

        if (response.statusCode == 200 || response.statusCode == 201) {
          final responseData = jsonDecode(response.body);
          final token = responseData['data']['token']; // Corrected to access nested token

          if (token == null || token.isEmpty) {
            setState(() {
              _errorMessage = AppLocalizations.tr(context, 'no_token_received') +
                  ': ${response.body}';
            });
            return;
          }

          // Store token securely
          await _storage.write(key: 'auth_token', value: token);

          // Verify token was stored
          String? storedToken = await _storage.read(key: 'auth_token');
          if (storedToken == null || storedToken != token) {
            setState(() {
              _errorMessage = AppLocalizations.tr(context, 'token_storage_failed');
            });
            return;
          }

          // Small delay to ensure storage completion
          await Future.delayed(const Duration(milliseconds: 100));

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.tr(context, 'signup_success')),
              backgroundColor: AppColors.primaryOrange,
            ),
          );

          // Navigate to DashboardScreen and remove all previous routes
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const DashboardScreen()),
            (route) => false,
          );
        } else {
          // Handle error response
          final errorData = jsonDecode(response.body);
          final errorMessage = errorData['message'] ?? AppLocalizations.tr(context, 'signup_failed');
          setState(() {
            _errorMessage = errorMessage + ': ${response.statusCode}';
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        // Handle network or other errors
        setState(() {
          _errorMessage = AppLocalizations.tr(context, 'network_error') + ': $e';
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.tr(context, 'network_error')),
            backgroundColor: Colors.red,
          ),
        );
        print('Error during signup: $e');
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.isArabic;
    final colors = ThemeHelper.getColorsWithListener(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isTablet = screenWidth > 600;

    return Scaffold(
      backgroundColor: colors.backgroundSecondary,
      body: Stack(
        children: [
          _buildBackgroundSection(context, isTablet),
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: _buildHeaderSection(isArabic, context, isTablet),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildBottomSheetSignup(
                isArabic, context, isTablet, screenHeight),
          ),
        ],
      ),
    );
  }

  Widget _buildBackgroundSection(BuildContext context, bool isTablet) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryOrange,
            AppColors.primaryOrange.withOpacity(0.9),
            AppColors.primaryOrange.withOpacity(0.7),
          ],
        ),
      ),
      child: CustomPaint(
        painter: _WavePainter(),
      ),
    );
  }

  Widget _buildHeaderSection(
      bool isArabic, BuildContext context, bool isTablet) {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: isTablet ? 32 : 20,
          vertical: isTablet ? 30 : 20,
        ),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: AppColors.pureWhite.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
                  ),
                  child: IconButton(
                    icon: Icon(
                      Icons.arrow_back,
                      color: AppColors.pureWhite,
                      size: isTablet ? 24 : 20,
                    ),
                    onPressed: () => Navigator.pop(context),
                  ),
                ),
                Expanded(
                  child: Text(
                    AppLocalizations.tr(context, 'create_account'),
                    style: TextStyle(
                      color: AppColors.pureWhite,
                      fontSize: isTablet ? 24 : 20,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          color: AppColors.primaryOrange.withOpacity(0.5),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(width: isTablet ? 48 : 40),
              ],
            ),
            SizedBox(height: isTablet ? 16 : 12),
            Text(
              AppLocalizations.tr(context, 'signup_subtitle'),
              style: TextStyle(
                color: AppColors.pureWhite.withOpacity(0.9),
                fontSize: isTablet ? 16 : 14,
                shadows: [
                  Shadow(
                    color: AppColors.primaryOrange.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomSheetSignup(
      bool isArabic, BuildContext context, bool isTablet, double screenHeight) {
    final colors = ThemeHelper.getColorsWithListener(context);
    final bottomSheetHeight = screenHeight * (isTablet ? 0.7 : 0.8);

    return Container(
      height: bottomSheetHeight,
      decoration: BoxDecoration(
        color: colors.backgroundPrimary,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(isTablet ? 32 : 28),
          topRight: Radius.circular(isTablet ? 32 : 28),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withOpacity(0.2),
            blurRadius: 20,
            spreadRadius: 5,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 32 : 24,
                vertical: isTablet ? 20 : 16,
              ),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildSignupHeader(context, isArabic, isTablet),
                    SizedBox(height: isTablet ? 24 : 20),
                    _buildFullNameField(context, isArabic, isTablet),
                    SizedBox(height: isTablet ? 16 : 14),
                    _buildEmailField(context, isArabic, isTablet),
                    SizedBox(height: isTablet ? 16 : 14),
                    _buildPhoneField(context, isArabic, isTablet),
                    SizedBox(height: isTablet ? 16 : 14),
                    _buildAddressField(context, isTablet),
                    SizedBox(height: isTablet ? 16 : 14),
                    _buildPasswordField(context, isArabic, isTablet),
                    SizedBox(height: isTablet ? 16 : 14),
                    _buildConfirmPasswordField(context, isArabic, isTablet),
                    if (_errorMessage != null) ...[
                      SizedBox(height: isTablet ? 16 : 14),
                      Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: Colors.red,
                          fontSize: isTablet ? 14 : 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                    SizedBox(height: isTablet ? 24 : 20),
                    _buildSignupButton(context, isArabic, isTablet),
                    SizedBox(height: isTablet ? 16 : 14),
                    _buildLoginLink(context, isArabic, isTablet),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSignupHeader(
      BuildContext context, bool isArabic, bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ShaderMask(
          shaderCallback: (bounds) => LinearGradient(
            colors: [
              AppColors.primaryOrange,
              AppColors.primaryOrange.withOpacity(0.8),
            ],
          ).createShader(bounds),
          child: Text(
            AppLocalizations.tr(context, 'welcome'),
            style: TextStyle(
              fontSize: isTablet ? 22 : 20,
              fontWeight: FontWeight.w700,
              color: AppColors.primaryOrange,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        SizedBox(height: isTablet ? 12 : 10),
        Text(
          AppLocalizations.tr(context, 'signup_subtitle'),
          style: TextStyle(
            fontSize: isTablet ? 16 : 14,
            fontWeight: FontWeight.w400,
            color: ThemeHelper.getColors(context).textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildFullNameField(
      BuildContext context, bool isArabic, bool isTablet) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextFormField(
        controller: _fullNameController,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validateName(value, context),
        style: TextStyle(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w400,
          color: Colors.black,
        ),
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: AppLocalizations.tr(context, 'full_name'),
          hintText: isArabic ? 'محمد أحمد' : 'Jean Dupont',
          prefixIcon: Icon(
            Icons.person_outline,
            color: AppColors.primaryOrange.withOpacity(0.7),
            size: isTablet ? 24 : 20,
          ),
        ).copyWith(
          contentPadding: EdgeInsets.symmetric(
            horizontal: isTablet ? 20 : 16,
            vertical: isTablet ? 18 : 16,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide(
              color: AppColors.primaryOrange,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: ThemeHelper.getColors(context).surface,
        ),
      ),
    );
  }

  Widget _buildEmailField(BuildContext context, bool isArabic, bool isTablet) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextFormField(
        controller: _emailController,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validateEmail(value, context),
        keyboardType: TextInputType.emailAddress,
        style: TextStyle(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w400,
          color: Colors.black,
        ),
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: AppLocalizations.tr(context, 'email'),
          hintText: '<EMAIL>',
          prefixIcon: Icon(
            Icons.email_outlined,
            color: AppColors.primaryOrange.withOpacity(0.7),
            size: isTablet ? 24 : 20,
          ),
        ).copyWith(
          contentPadding: EdgeInsets.symmetric(
            horizontal: isTablet ? 20 : 16,
            vertical: isTablet ? 18 : 16,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide(
              color: AppColors.primaryOrange,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: ThemeHelper.getColors(context).surface,
        ),
      ),
    );
  }

  Widget _buildPhoneField(BuildContext context, bool isArabic, bool isTablet) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextFormField(
        controller: _phoneController,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validatePhone(value, context),
        keyboardType: TextInputType.phone,
        style: TextStyle(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w400,
          color: Colors.black,
        ),
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: AppLocalizations.tr(context, 'phone'),
          hintText: isArabic ? '05xxxxxxxx' : '+33xxxxxxxxx',
          prefixIcon: Icon(
            Icons.phone_outlined,
            color: AppColors.primaryOrange.withOpacity(0.7),
            size: isTablet ? 24 : 20,
          ),
        ).copyWith(
          contentPadding: EdgeInsets.symmetric(
            horizontal: isTablet ? 20 : 16,
            vertical: isTablet ? 18 : 16,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide(
              color: AppColors.primaryOrange,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: ThemeHelper.getColors(context).surface,
        ),
      ),
    );
  }

  Widget _buildAddressField(BuildContext context, bool isTablet) {
    final isArabic = Provider.of<LocaleProvider>(context).isArabic;
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextFormField(
        controller: _addressController,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validateAddress(value, context),
        style: TextStyle(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w400,
          color: Colors.black,
        ),
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: AppLocalizations.tr(context, 'address'),
          hintText: isArabic ? '123 شارع الاختبار' : '123 Test Street',
          prefixIcon: Icon(
            Icons.location_on_outlined,
            color: AppColors.primaryOrange.withOpacity(0.7),
            size: isTablet ? 24 : 20,
          ),
        ).copyWith(
          contentPadding: EdgeInsets.symmetric(
            horizontal: isTablet ? 20 : 16,
            vertical: isTablet ? 18 : 16,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide(
              color: AppColors.primaryOrange,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: ThemeHelper.getColors(context).surface,
        ),
      ),
    );
  }

  Widget _buildPasswordField(
      BuildContext context, bool isArabic, bool isTablet) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextFormField(
        controller: _passwordController,
        obscureText: true,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validatePassword(value, context),
        style: TextStyle(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w400,
          color: Colors.black,
        ),
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: AppLocalizations.tr(context, 'password'),
          hintText: '********',
          prefixIcon: Icon(
            Icons.lock_outline,
            color: AppColors.primaryOrange.withOpacity(0.7),
            size: isTablet ? 24 : 20,
          ),
        ).copyWith(
          contentPadding: EdgeInsets.symmetric(
            horizontal: isTablet ? 20 : 16,
            vertical: isTablet ? 18 : 16,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide(
              color: AppColors.primaryOrange,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: ThemeHelper.getColors(context).surface,
        ),
      ),
    );
  }

  Widget _buildConfirmPasswordField(
      BuildContext context, bool isArabic, bool isTablet) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextFormField(
        controller: _confirmPasswordController,
        obscureText: true,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validateConfirmPassword(value, context),
        style: TextStyle(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w400,
          color: Colors.black,
        ),
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: AppLocalizations.tr(context, 'confirm_password'),
          hintText: '********',
          prefixIcon: Icon(
            Icons.lock_outline,
            color: AppColors.primaryOrange.withOpacity(0.7),
            size: isTablet ? 24 : 20,
          ),
        ).copyWith(
          contentPadding: EdgeInsets.symmetric(
            horizontal: isTablet ? 20 : 16,
            vertical: isTablet ? 18 : 16,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide(
              color: AppColors.primaryOrange,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: ThemeHelper.getColors(context).surface,
        ),
      ),
    );
  }

  Widget _buildSignupButton(
      BuildContext context, bool isArabic, bool isTablet) {
    return Container(
      width: double.infinity,
      height: isTablet ? 56 : 52,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryOrange,
            AppColors.primaryOrange.withOpacity(0.9),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleSignup,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: AppColors.pureWhite,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
          ),
        ),
        child: _isLoading
            ? CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.pureWhite),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    AppLocalizations.tr(context, 'sign_up'),
                    style: TextStyle(
                      fontSize: isTablet ? 18 : 16,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.5,
                    ),
                  ),
                  SizedBox(width: isTablet ? 12 : 10),
                  Icon(
                    Icons.arrow_forward,
                    size: isTablet ? 20 : 18,
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildLoginLink(BuildContext context, bool isArabic, bool isTablet) {
    return Align(
      alignment: Alignment.center,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: isTablet ? 12.0 : 10.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              AppLocalizations.tr(context, 'already_have_account'),
              style: TextStyle(
                fontSize: isTablet ? 16 : 14,
                color: ThemeHelper.getColors(context).textSecondary,
              ),
            ),
            SizedBox(width: isTablet ? 8 : 6),
            GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isTablet ? 12 : 10,
                  vertical: isTablet ? 6 : 4,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primaryOrange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(isTablet ? 8 : 6),
                ),
                child: Text(
                  AppLocalizations.tr(context, 'login'),
                  style: TextStyle(
                    color: AppColors.primaryOrange,
                    fontSize: isTablet ? 16 : 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _WavePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.pureWhite.withOpacity(0.1)
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(0, size.height * 0.7);
    path.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.6,
      size.width * 0.5,
      size.height * 0.7,
    );
    path.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.8,
      size.width,
      size.height * 0.7,
    );
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);

    final paint2 = Paint()
      ..color = AppColors.pureWhite.withOpacity(0.05)
      ..style = PaintingStyle.fill;

    final path2 = Path();
    path2.moveTo(0, size.height * 0.8);
    path2.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.7,
      size.width * 0.6,
      size.height * 0.8,
    );
    path2.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.9,
      size.width,
      size.height * 0.8,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    canvas.drawPath(path2, paint2);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}