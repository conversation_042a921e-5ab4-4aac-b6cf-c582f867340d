import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum AppThemeMode { light, dark }

class Theme<PERSON>rovider extends ChangeNotifier {
  AppThemeMode _themeMode = AppThemeMode.dark;
  bool _isDarkMode = true;

  AppThemeMode get themeMode => _themeMode;
  bool get isDarkMode => _isDarkMode;

  ThemeProvider() {
    _loadThemeFromPrefs();
    _updateThemeBasedOnMode();
  }

  void setThemeMode(AppThemeMode mode) {
    if (_themeMode == mode) return;
    _themeMode = mode;
    _updateThemeBasedOnMode();
    _saveThemeToPrefs();
    notifyListeners();
  }

  void _updateThemeBasedOnMode() {
    switch (_themeMode) {
      case AppThemeMode.light:
        _isDarkMode = false;
        break;
      case AppThemeMode.dark:
        _isDarkMode = true;
        break;
    }
  }

  Future<void> _loadThemeFromPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeModeIndex = prefs.getInt('theme_mode') ?? AppThemeMode.dark.index;
      _themeMode = AppThemeMode.values[themeModeIndex];
      _updateThemeBasedOnMode();
    } catch (e) {
      // If there's an error loading preferences, use default (dark mode)
      _themeMode = AppThemeMode.dark;
      _updateThemeBasedOnMode();
    }
  }

  Future<void> _saveThemeToPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('theme_mode', _themeMode.index);
    } catch (e) {
      // Handle error silently
    }
  }
}
