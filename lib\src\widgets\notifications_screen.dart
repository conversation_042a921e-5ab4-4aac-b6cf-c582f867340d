import 'package:droit/src/config/colors.dart';
import 'package:droit/src/config/theme_helper.dart';
import 'package:droit/src/config/app_localizations.dart';
import 'package:droit/src/widgets/locale_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final List<Map<String, dynamic>> notifications = [
    {
      'type': 'rejection',
      'requestId': 'DEM-2023-001',
      'date': DateTime.now().subtract(const Duration(days: 1)),
      'message': 'Votre demande a été refusée',
      'messageAr': 'تم رفض طلبك',
      'details': 'Documents incomplets',
      'detailsAr': 'وثائق غير مكتملة',
    },
    {
      'type': 'approval',
      'requestId': 'DEM-2023-002',
      'date': DateTime.now().subtract(const Duration(days: 2)),
      'message': 'Votre demande a été approuvée',
      'messageAr': 'تمت الموافقة على طلبك',
      'details': 'Vous pouvez maintenant procéder au paiement',
      'detailsAr': 'يمكنك الآن المتابعة للدفع',
    },
    {
      'type': 'expiration',
      'requestId': 'DEM-2023-003',
      'date': DateTime.now().subtract(const Duration(days: 3)),
      'message': 'Votre permis expire bientôt',
      'messageAr': 'تصريحك على وشك الانتهاء',
      'details': 'Expiration dans 30 jours',
      'detailsAr': 'ينتهي في غضون 30 يومًا',
      'expiryDate': DateTime.now().add(const Duration(days: 30)),
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.isArabic;
    final colors = ThemeHelper.getColorsWithListener(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      backgroundColor: colors.backgroundSecondary,
      body: Column(
        children: [
          _buildHeaderSection(isArabic, context, isTablet),
          Expanded(
            child: notifications.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.notifications_none,
                          size: isTablet ? 64 : 48,
                          color: colors.textSecondary.withOpacity(0.5),
                        ),
                        SizedBox(height: isTablet ? 16 : 12),
                        Text(
                          AppLocalizations.tr(context, 'no_notifications'),
                          style: ThemeHelper.getSectionTitleStyle(context)
                              .copyWith(
                            fontSize: isTablet ? 18 : 16,
                            color: colors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: EdgeInsets.symmetric(
                      horizontal: isTablet ? 24 : 12,
                      vertical: isTablet ? 16 : 12,
                    ),
                    itemCount: notifications.length,
                    itemBuilder: (context, index) {
                      return _buildNotificationCard(
                          notifications[index], isArabic, context, isTablet);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderSection(
      bool isArabic, BuildContext context, bool isTablet) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryOrange,
            AppColors.primaryOrange.withOpacity(0.8),
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? 24 : 16,
            vertical: isTablet ? 20 : 16,
          ),
          child: Row(
            children: [
              IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: AppColors.pureWhite,
                  size: isTablet ? 24 : 20,
                ),
                onPressed: () => Navigator.pop(context),
              ),
              SizedBox(width: isTablet ? 12 : 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.tr(context, 'notifications'),
                      style: ThemeHelper.getTitleStyle(context).copyWith(
                        color: AppColors.pureWhite,
                        fontSize: isTablet ? 24 : 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      AppLocalizations.tr(context, 'stay_updated'),
                      style: ThemeHelper.getSubtitleStyle(context).copyWith(
                        color: AppColors.pureWhite.withOpacity(0.8),
                        fontSize: isTablet ? 14 : 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationCard(Map<String, dynamic> notification,
      bool isArabic, BuildContext context, bool isTablet) {
    final colors = ThemeHelper.getColors(context);
    IconData iconData;
    Color iconColor;

    switch (notification['type']) {
      case 'rejection':
        iconData = Icons.cancel_outlined;
        iconColor = AppColors.error;
        break;
      case 'approval':
        iconData = Icons.check_circle_outline;
        iconColor = AppColors.success;
        break;
      case 'expiration':
        iconData = Icons.access_time;
        iconColor = AppColors.warning;
        break;
      default:
        iconData = Icons.notifications_outlined;
        iconColor = AppColors.info;
    }

    return Container(
      margin: EdgeInsets.only(bottom: isTablet ? 12 : 10),
      decoration: BoxDecoration(
        color: colors.card,
        borderRadius: BorderRadius.circular(isTablet ? 16 : 12),
        boxShadow: [
          BoxShadow(
            color: colors.shadowColor.withOpacity(0.06),
            blurRadius: isTablet ? 12 : 8,
            offset: const Offset(0, 3),
          ),
        ],
        border: Border.all(
          color: iconColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 16 : 14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isTablet ? 12 : 10),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        iconColor.withOpacity(0.15),
                        iconColor.withOpacity(0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
                  ),
                  child: Icon(
                    iconData,
                    color: iconColor,
                    size: isTablet ? 22 : 18,
                  ),
                ),
                SizedBox(width: isTablet ? 12 : 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isArabic
                            ? notification['messageAr']
                            : notification['message'],
                        style:
                            ThemeHelper.getSectionTitleStyle(context).copyWith(
                          fontSize: isTablet ? 16 : 15,
                          fontWeight: FontWeight.w600,
                          color: colors.textPrimary,
                        ),
                      ),
                      SizedBox(height: isTablet ? 4 : 2),
                      Text(
                        '${isArabic ? 'رقم الطلب: ' : 'Demande N°: '}${notification['requestId']}',
                        style: ThemeHelper.getSubtitleStyle(context).copyWith(
                          fontSize: isTablet ? 13 : 12,
                          color: colors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: isTablet ? 8 : 6,
                    vertical: isTablet ? 4 : 3,
                  ),
                  decoration: BoxDecoration(
                    color: iconColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(isTablet ? 8 : 6),
                  ),
                  child: Text(
                    _formatTimeAgo(notification['date'], isArabic),
                    style: TextStyle(
                      color: iconColor,
                      fontSize: isTablet ? 11 : 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: isTablet ? 12 : 10),
            Container(
              padding: EdgeInsets.all(isTablet ? 12 : 10),
              decoration: BoxDecoration(
                color: colors.surface,
                borderRadius: BorderRadius.circular(isTablet ? 10 : 8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: colors.textSecondary,
                    size: isTablet ? 16 : 14,
                  ),
                  SizedBox(width: isTablet ? 8 : 6),
                  Expanded(
                    child: Text(
                      isArabic
                          ? notification['detailsAr']
                          : notification['details'],
                      style: ThemeHelper.getSubtitleStyle(context).copyWith(
                        fontSize: isTablet ? 13 : 12,
                        color: colors.textSecondary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            if (notification['type'] == 'expiration' &&
                notification['expiryDate'] != null) ...[
              SizedBox(height: isTablet ? 12 : 10),
              _buildExpirationWarning(
                  notification['expiryDate'] as DateTime, isArabic, isTablet),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildExpirationWarning(
      DateTime expiryDate, bool isArabic, bool isTablet) {
    final daysRemaining = expiryDate.difference(DateTime.now()).inDays;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        vertical: isTablet ? 12 : 10,
        horizontal: isTablet ? 16 : 12,
      ),
      decoration: BoxDecoration(
        color: AppColors.warning.withOpacity(0.15),
        borderRadius: BorderRadius.circular(isTablet ? 10 : 8),
        border: Border.all(
          color: AppColors.warning.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: AppColors.warning,
            size: isTablet ? 20 : 18,
          ),
          SizedBox(width: isTablet ? 12 : 10),
          Expanded(
            child: Text(
              isArabic
                  ? 'متبقي $daysRemaining يوم على انتهاء التصريح'
                  : 'Il reste $daysRemaining jours avant expiration',
              style: TextStyle(
                color: AppColors.warning,
                fontWeight: FontWeight.w600,
                fontSize: isTablet ? 13 : 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimeAgo(DateTime date, bool isArabic) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return isArabic ? '${difference.inDays}د' : '${difference.inDays}j';
    } else if (difference.inHours > 0) {
      return isArabic ? '${difference.inHours}س' : '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return isArabic ? '${difference.inMinutes}د' : '${difference.inMinutes}m';
    } else {
      return isArabic ? 'الآن' : 'maintenant';
    }
  }
}
