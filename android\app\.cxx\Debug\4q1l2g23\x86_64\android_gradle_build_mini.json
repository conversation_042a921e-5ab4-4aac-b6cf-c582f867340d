{"buildFiles": ["C:\\flutter_windows_3.27.3-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\FLUTTER\\droit\\droit\\android\\app\\.cxx\\Debug\\4q1l2g23\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\FLUTTER\\droit\\droit\\android\\app\\.cxx\\Debug\\4q1l2g23\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}