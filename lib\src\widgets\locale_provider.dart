import 'package:flutter/material.dart';

class LocaleProvider extends ChangeNotifier {
  Locale _locale = const Locale('ar', 'SA'); // Default to Arabic

  Locale get locale => _locale;

  void setLocale(Locale locale) {
    if (_locale == locale) return; // Avoid unnecessary updates
    if (locale.languageCode == 'ar' || locale.languageCode == 'fr' || locale.languageCode == 'en') {
      _locale = locale;
      notifyListeners(); // Trigger UI rebuild with new locale
    }
  }

  // Helper method to get current language code
  String get currentLanguage => _locale.languageCode;

  // Helper method to check if current language is Arabic (for RTL)
  bool get isArabic => _locale.languageCode == 'ar';

  // Helper method to check if current language is French
  bool get isFrench => _locale.languageCode == 'fr';

  // Helper method to check if current language is English
  bool get isEnglish => _locale.languageCode == 'en';
}