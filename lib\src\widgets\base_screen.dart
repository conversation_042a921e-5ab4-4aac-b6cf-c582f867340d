import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import 'locale_provider.dart';
import 'notifications_screen.dart';
import '../widgets/dashboard.dart';
import 'settings.dart';
import 'history_screen.dart';

class BaseScreen extends StatefulWidget {
  final Widget child;
  final int currentIndex;
  final bool showAppBar;

  const BaseScreen({
    super.key,
    required this.child,
    required this.currentIndex,
    this.showAppBar = true,
  });

  @override
  State<BaseScreen> createState() => _BaseScreenState();
}

class _BaseScreenState extends State<BaseScreen> {
  @override
  Widget build(BuildContext context) {
    final colors = ThemeHelper.getColorsWithListener(context);

    return Scaffold(
      backgroundColor: colors.backgroundSecondary,
      appBar: widget.showAppBar
          ? AppBar(
              automaticallyImplyLeading: false,
              backgroundColor: colors.backgroundSecondary,
              elevation: 0,
              title: Text(
                AppLocalizations.tr(context, 'app_title'),
                style: ThemeHelper.getSectionTitleStyle(context),
              ),
              actions: [
                IconButton(
                  icon: const Icon(
                    Icons.notifications,
                    color: AppColors.primaryOrange,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const NotificationsScreen(),
                      ),
                    );
                  },
                ),
              ],
            )
          : null,
      body: widget.child,
      bottomNavigationBar: BottomNavigationBar(
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.home),
            label: AppLocalizations.tr(context, 'home'),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.history),
            label: AppLocalizations.tr(context, 'history'),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.settings),
            label: AppLocalizations.tr(context, 'settings'),
          ),
        ],
        currentIndex: widget.currentIndex,
        selectedItemColor: AppColors.primaryOrange,
        unselectedItemColor: colors.textSecondary,
        backgroundColor: colors.surface,
        onTap: (index) {
          if (index != widget.currentIndex) {
            Widget nextScreen;
            switch (index) {
              case 0:
                nextScreen = const DashboardScreen();
                break;
              case 1:
                nextScreen = const HistoryScreen();
                break;
              case 2:
                nextScreen = const SettingsScreen();
                break;
              default:
                return;
            }
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => nextScreen),
            );
          }
        },
        type: BottomNavigationBarType.fixed,
      ),
    );
  }
}
