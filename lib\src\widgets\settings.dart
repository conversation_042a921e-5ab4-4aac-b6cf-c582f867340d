import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/models/constants.dart';
import '../config/theme_provider.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import '../widgets/locale_provider.dart';
import '../widgets/signin.dart';
import 'base_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _storage = const FlutterSecureStorage();
  Map<String, dynamic>? _userProfile;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchUserProfile();
  }

  Future<void> _fetchUserProfile() async {
    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null || token.isEmpty) {
        setState(() {
          _errorMessage = AppLocalizations.tr(context, 'no_token');
          _isLoading = false;
        });
        return;
      }

      final response = await http.get(
        Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/profile'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('Profile API Response: ${response.statusCode} - ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['data'] != null) {
          setState(() {
            _userProfile = responseData['data'];
            _isLoading = false;
          });
        } else {
          setState(() {
            _errorMessage = AppLocalizations.tr(context, 'no_profile_data') +
                ': ${response.body}';
            _isLoading = false;
          });
        }
      } else {
        setState(() {
          _errorMessage = jsonDecode(response.body)['message'] ??
              AppLocalizations.tr(context, 'fetch_profile_failed') +
                  ': ${response.statusCode}';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = AppLocalizations.tr(context, 'network_error') + ': $e';
        _isLoading = false;
      });
      print('Error fetching profile: $e');
    }
  }

  Future<void> _handleLogout(BuildContext context) async {
    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null || token.isEmpty) {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const SignInScreen()),
          (route) => false,
        );
        return;
      }

      final response = await http.post(
        Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/logout'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('Logout API Response: ${response.statusCode} - ${response.body}');

      if (response.statusCode == 200) {
        await _storage.delete(key: 'auth_token');
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const SignInScreen()),
          (route) => false,
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              jsonDecode(response.body)['message'] ??
                  AppLocalizations.tr(context, 'logout_failed'),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.tr(context, 'network_error')),
          backgroundColor: Colors.red,
        ),
      );
      print('Error during logout: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.isArabic;

    return BaseScreen(
      currentIndex: 2,
      showAppBar: false,
      child: SingleChildScrollView(
        child: Column(
          children: [
            _buildHeaderSection(isArabic, context),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : _errorMessage != null
                          ? Center(
                              child: Text(
                                _errorMessage!,
                                style: const TextStyle(color: Colors.red),
                              ),
                            )
                          : _buildProfileSection(isArabic, context),
                  const SizedBox(height: 12),
                  _buildLanguageSection(isArabic, localeProvider, context),
                  const SizedBox(height: 12),
                  _buildThemeSection(isArabic, context),
                  const SizedBox(height: 12),
                  _buildNotificationSection(isArabic, context),
                  const SizedBox(height: 12),
                  _buildPrivacySection(isArabic, context),
                  const SizedBox(height: 12),
                  _buildAboutSection(isArabic, context),
                  const SizedBox(height: 12),
                  _buildLogoutButton(isArabic, context),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection(bool isArabic, BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryOrange,
            AppColors.primaryOrange.withOpacity(0.8),
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppLocalizations.tr(context, 'settings'),
                    style: ThemeHelper.getTitleStyle(context).copyWith(
                      color: AppColors.pureWhite,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: isArabic ? TextAlign.right : TextAlign.left,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    AppLocalizations.tr(context, 'manage_your_preferences'),
                    style: ThemeHelper.getSubtitleStyle(context).copyWith(
                      color: AppColors.pureWhite.withOpacity(0.8),
                      fontSize: 12,
                    ),
                    textAlign: isArabic ? TextAlign.right : TextAlign.left,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileSection(bool isArabic, BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final colors = ThemeHelper.getColorsWithListener(context);
        return Container(
          decoration: BoxDecoration(
            color: colors.card,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: colors.shadowColor.withOpacity(0.06),
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(14),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.person_outline,
                      color: AppColors.primaryOrange,
                      size: 20,
                    ),
                    const SizedBox(width: 10),
                    Text(
                      AppLocalizations.tr(context, 'profile'),
                      style: ThemeHelper.getSectionTitleStyle(context).copyWith(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Stack(
                      children: [
                        CircleAvatar(
                          radius: 28,
                          backgroundColor:
                              AppColors.primaryOrange.withOpacity(0.1),
                          child: CircleAvatar(
                            radius: 26,
                            backgroundColor: AppColors.primaryOrange,
                            child: const Icon(
                              Icons.person,
                              color: AppColors.pureWhite,
                              size: 24,
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: Container(
                            decoration: BoxDecoration(
                              color: AppColors.primaryOrange,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: colors.card,
                                width: 2,
                              ),
                            ),
                            child: const Icon(
                              Icons.camera_alt,
                              color: AppColors.pureWhite,
                              size: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _userProfile?['name'] ?? 'Unknown',
                            style:
                                ThemeHelper.getSubtitleStyle(context).copyWith(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: colors.textPrimary,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _userProfile?['email'] ?? '<EMAIL>',
                            style:
                                ThemeHelper.getSubtitleStyle(context).copyWith(
                              fontSize: 12,
                              color: colors.textSecondary,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.primaryOrange.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              AppLocalizations.tr(context, 'verified'),
                              style: TextStyle(
                                color: AppColors.primaryOrange,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder: (context) => EditProfileBottomSheet(
                          initialProfile: _userProfile,
                          onProfileUpdated: _fetchUserProfile,
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryOrange.withOpacity(0.1),
                      foregroundColor: AppColors.primaryOrange,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: AppColors.primaryOrange.withOpacity(0.3),
                        ),
                      ),
                    ),
                    icon: const Icon(Icons.edit_outlined, size: 16),
                    label: Text(
                      AppLocalizations.tr(context, 'edit_profile'),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLanguageSection(
      bool isArabic, LocaleProvider localeProvider, BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final colors = ThemeHelper.getColorsWithListener(context);
        return Container(
          decoration: BoxDecoration(
            color: colors.card,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: colors.shadowColor.withOpacity(0.06),
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(14),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.language_outlined,
                      color: AppColors.primaryOrange,
                      size: 20,
                    ),
                    const SizedBox(width: 10),
                    Text(
                      AppLocalizations.tr(context, 'language'),
                      style: ThemeHelper.getSectionTitleStyle(context).copyWith(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  AppLocalizations.tr(context, 'choose_language'),
                  style: ThemeHelper.getSubtitleStyle(context).copyWith(
                    color: colors.textSecondary,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 12),
                Column(
                  children: [
                    _buildLanguageOption(
                      context,
                      isArabic,
                      localeProvider,
                      'ar',
                      'العربية',
                      '🇸🇦',
                    ),
                    const SizedBox(height: 6),
                    _buildLanguageOption(
                      context,
                      isArabic,
                      localeProvider,
                      'fr',
                      'Français',
                      '🇫🇷',
                    ),
                    const SizedBox(height: 6),
                    _buildLanguageOption(
                      context,
                      isArabic,
                      localeProvider,
                      'en',
                      'English',
                      '🇺🇸',
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLanguageOption(
    BuildContext context,
    bool isArabic,
    LocaleProvider localeProvider,
    String languageCode,
    String languageName,
    String flag,
  ) {
    final isSelected = localeProvider.currentLanguage == languageCode;
    final colors = ThemeHelper.getColorsWithListener(context);

    return InkWell(
      onTap: () {
        if (languageCode == 'ar') {
          localeProvider.setLocale(const Locale('ar', 'SA'));
        } else if (languageCode == 'fr') {
          localeProvider.setLocale(const Locale('fr'));
        } else if (languageCode == 'en') {
          localeProvider.setLocale(const Locale('en'));
        }
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: isSelected
              ? AppColors.primaryOrange.withOpacity(0.1)
              : colors.surface,
          border: Border.all(
            color: isSelected ? AppColors.primaryOrange : colors.borderPrimary,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Text(
              flag,
              style: const TextStyle(fontSize: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                languageName,
                style: ThemeHelper.getSubtitleStyle(context).copyWith(
                  color:
                      isSelected ? AppColors.primaryOrange : colors.textPrimary,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  fontSize: 14,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppColors.primaryOrange,
                size: 18,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeSection(bool isArabic, BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final colors = ThemeHelper.getColorsWithListener(context);
        return Container(
          decoration: BoxDecoration(
            color: colors.card,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: colors.shadowColor.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.palette_outlined,
                      color: AppColors.primaryOrange,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      AppLocalizations.tr(context, 'appearance'),
                      style: ThemeHelper.getSectionTitleStyle(context).copyWith(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  AppLocalizations.tr(context, 'choose_theme'),
                  style: ThemeHelper.getSubtitleStyle(context).copyWith(
                    color: colors.textSecondary,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildThemeCard(
                        context,
                        themeProvider,
                        AppThemeMode.light,
                        AppLocalizations.tr(context, 'light'),
                        Icons.light_mode,
                        Colors.white,
                        Colors.grey[100]!,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildThemeCard(
                        context,
                        themeProvider,
                        AppThemeMode.dark,
                        AppLocalizations.tr(context, 'dark'),
                        Icons.dark_mode,
                        Colors.grey[900]!,
                        Colors.grey[800]!,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildThemeCard(
    BuildContext context,
    ThemeProvider themeProvider,
    AppThemeMode mode,
    String title,
    IconData icon,
    Color backgroundColor,
    Color surfaceColor,
  ) {
    final isSelected = themeProvider.themeMode == mode;
    final colors = ThemeHelper.getColorsWithListener(context);

    return InkWell(
      onTap: () => themeProvider.setThemeMode(mode),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppColors.primaryOrange : colors.borderPrimary,
            width: isSelected ? 2 : 1,
          ),
          color: isSelected
              ? AppColors.primaryOrange.withOpacity(0.05)
              : colors.surface,
        ),
        child: Column(
          children: [
            // Theme preview
            Container(
              height: 60,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: backgroundColor,
                border: Border.all(
                  color: colors.borderPrimary.withOpacity(0.3),
                ),
              ),
              child: Column(
                children: [
                  Container(
                    height: 20,
                    decoration: BoxDecoration(
                      color: surfaceColor,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    ),
                    child: Row(
                      children: [
                        const SizedBox(width: 8),
                        Container(
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            color: AppColors.primaryOrange,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Container(
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            color: Colors.grey,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            height: 4,
                            width: 40,
                            decoration: BoxDecoration(
                              color: mode == AppThemeMode.light
                                  ? Colors.grey[800]
                                  : Colors.grey[300],
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Container(
                            height: 3,
                            width: 60,
                            decoration: BoxDecoration(
                              color: mode == AppThemeMode.light
                                  ? Colors.grey[600]
                                  : Colors.grey[400],
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: isSelected
                      ? AppColors.primaryOrange
                      : colors.textSecondary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: ThemeHelper.getSubtitleStyle(context).copyWith(
                    color: isSelected
                        ? AppColors.primaryOrange
                        : colors.textPrimary,
                    fontWeight:
                        isSelected ? FontWeight.w600 : FontWeight.normal,
                    fontSize: 14,
                  ),
                ),
                if (isSelected) ...[
                  const SizedBox(width: 8),
                  Icon(
                    Icons.check_circle,
                    color: AppColors.primaryOrange,
                    size: 16,
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSection(bool isArabic, BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final colors = ThemeHelper.getColorsWithListener(context);
        return Container(
          decoration: BoxDecoration(
            color: colors.card,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: colors.shadowColor.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.notifications_outlined,
                      color: AppColors.primaryOrange,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      AppLocalizations.tr(context, 'notifications'),
                      style: ThemeHelper.getSectionTitleStyle(context).copyWith(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildSettingsTile(
                  context,
                  Icons.notifications_active_outlined,
                  AppLocalizations.tr(context, 'push_notifications'),
                  AppLocalizations.tr(context, 'receive_notifications'),
                  true,
                  (value) {},
                ),
                const SizedBox(height: 8),
                _buildSettingsTile(
                  context,
                  Icons.email_outlined,
                  AppLocalizations.tr(context, 'email_notifications'),
                  AppLocalizations.tr(context, 'receive_email_updates'),
                  false,
                  (value) {},
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPrivacySection(bool isArabic, BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final colors = ThemeHelper.getColorsWithListener(context);
        return Container(
          decoration: BoxDecoration(
            color: colors.card,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: colors.shadowColor.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.security_outlined,
                      color: AppColors.primaryOrange,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      AppLocalizations.tr(context, 'privacy_security'),
                      style: ThemeHelper.getSectionTitleStyle(context).copyWith(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildActionTile(
                  context,
                  Icons.lock_outline,
                  AppLocalizations.tr(context, 'change_password'),
                  AppLocalizations.tr(context, 'update_your_password'),
                  () {},
                ),
                const SizedBox(height: 8),
                _buildActionTile(
                  context,
                  Icons.privacy_tip_outlined,
                  AppLocalizations.tr(context, 'privacy_policy'),
                  AppLocalizations.tr(context, 'read_privacy_policy'),
                  () {},
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAboutSection(bool isArabic, BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final colors = ThemeHelper.getColorsWithListener(context);
        return Container(
          decoration: BoxDecoration(
            color: colors.card,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: colors.shadowColor.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: AppColors.primaryOrange,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      AppLocalizations.tr(context, 'about'),
                      style: ThemeHelper.getSectionTitleStyle(context).copyWith(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildActionTile(
                  context,
                  Icons.help_outline,
                  AppLocalizations.tr(context, 'help_support'),
                  AppLocalizations.tr(context, 'get_help_support'),
                  () {},
                ),
                const SizedBox(height: 8),
                _buildActionTile(
                  context,
                  Icons.star_outline,
                  AppLocalizations.tr(context, 'rate_app'),
                  AppLocalizations.tr(context, 'rate_us_store'),
                  () {},
                ),
                const SizedBox(height: 8),
                _buildInfoTile(
                  context,
                  Icons.info,
                  AppLocalizations.tr(context, 'version'),
                  '1.0.0',
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLogoutButton(bool isArabic, BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: [
            AppColors.error,
            AppColors.error.withOpacity(0.8),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.error.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text(AppLocalizations.tr(context, 'logout')),
              content:
                  Text(AppLocalizations.tr(context, 'logout_confirmation')),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(AppLocalizations.tr(context, 'cancel')),
                ),
                ElevatedButton(
                  onPressed: () => _handleLogout(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.error,
                  ),
                  child: Text(
                    AppLocalizations.tr(context, 'logout'),
                    style: const TextStyle(color: AppColors.pureWhite),
                  ),
                ),
              ],
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: AppColors.pureWhite,
          elevation: 0,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        icon: const Icon(Icons.logout, size: 20),
        label: Text(
          AppLocalizations.tr(context, 'logout'),
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsTile(
    BuildContext context,
    IconData icon,
    String title,
    String subtitle,
    bool value,
    Function(bool) onChanged,
  ) {
    final colors = ThemeHelper.getColorsWithListener(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colors.borderPrimary.withOpacity(0.5),
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppColors.primaryOrange,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: ThemeHelper.getSubtitleStyle(context).copyWith(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: colors.textPrimary,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: ThemeHelper.getSubtitleStyle(context).copyWith(
                    fontSize: 12,
                    color: colors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primaryOrange,
            activeTrackColor: AppColors.primaryOrange.withOpacity(0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildActionTile(
    BuildContext context,
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onTap,
  ) {
    final colors = ThemeHelper.getColorsWithListener(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: colors.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: colors.borderPrimary.withOpacity(0.5),
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: AppColors.primaryOrange,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: ThemeHelper.getSubtitleStyle(context).copyWith(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: colors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: ThemeHelper.getSubtitleStyle(context).copyWith(
                      fontSize: 12,
                      color: colors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: colors.textSecondary,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoTile(
    BuildContext context,
    IconData icon,
    String title,
    String value,
  ) {
    final colors = ThemeHelper.getColorsWithListener(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colors.borderPrimary.withOpacity(0.5),
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppColors.primaryOrange,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: ThemeHelper.getSubtitleStyle(context).copyWith(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: colors.textPrimary,
              ),
            ),
          ),
          Text(
            value,
            style: ThemeHelper.getSubtitleStyle(context).copyWith(
              fontSize: 14,
              color: colors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}

class EditProfileBottomSheet extends StatefulWidget {
  final Map<String, dynamic>? initialProfile;
  final VoidCallback onProfileUpdated;

  const EditProfileBottomSheet({
    super.key,
    this.initialProfile,
    required this.onProfileUpdated,
  });

  @override
  State<EditProfileBottomSheet> createState() => _EditProfileBottomSheetState();
}

class _EditProfileBottomSheetState extends State<EditProfileBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _storage = const FlutterSecureStorage();
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  late LocaleProvider localeProvider;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _nameController.text = widget.initialProfile?['name'] ?? '';
    _emailController.text = widget.initialProfile?['email'] ?? '';
    _phoneController.text = widget.initialProfile?['phoneNumber'] ?? ''; // Changed from 'phone' to 'phoneNumber'
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    localeProvider = Provider.of<LocaleProvider>(context, listen: false);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _handleSubmit() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      try {
        final token = await _storage.read(key: 'auth_token');
        if (token == null || token.isEmpty) {
          setState(() {
            _errorMessage = AppLocalizations.tr(context, 'no_token');
            _isLoading = false;
          });
          return;
        }

        final body = {
          'name': _nameController.text.trim(),
          'email': _emailController.text.trim(),
          if (_phoneController.text.isNotEmpty)
            'phoneNumber': _phoneController.text.trim(), // Changed from 'phone' to 'phoneNumber'
          if (_passwordController.text.isNotEmpty)
            'password': _passwordController.text,
          if (_passwordController.text.isNotEmpty)
            'password_confirmation': _confirmPasswordController.text,
        };

        final response = await http.patch(
          Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/profile'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: jsonEncode(body),
        );

        print('Update Profile API Response: ${response.statusCode} - ${response.body}');

        if (response.statusCode == 200) {
          final responseData = jsonDecode(response.body);
          if (responseData['data'] != null) {
            await Future.delayed(const Duration(milliseconds: 100)); // Small delay
            widget.onProfileUpdated();
            if (mounted) {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    AppLocalizations.tr(context, 'profile_updated_success'),
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } else {
            setState(() {
              _errorMessage = AppLocalizations.tr(context, 'no_profile_data') +
                  ': ${response.body}';
              _isLoading = false;
            });
          }
        } else {
          setState(() {
            _errorMessage = jsonDecode(response.body)['message'] ??
                AppLocalizations.tr(context, 'profile_update_error') +
                    ': ${response.statusCode}';
            _isLoading = false;
          });
        }
      } catch (e) {
        setState(() {
          _errorMessage = AppLocalizations.tr(context, 'network_error') + ': $e';
          _isLoading = false;
        });
        print('Error updating profile: $e');
      }
    }
  }

  Widget _buildTextField({
    required BuildContext context,
    required TextEditingController controller,
    required String label,
    required bool isArabic,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    bool obscureText = false,
    bool? obscureState,
    VoidCallback? onToggleObscure,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: obscureState ?? obscureText,
      keyboardType: keyboardType,
      textAlign: isArabic ? TextAlign.right : TextAlign.left,
      decoration: ThemeHelper.getInputDecoration(
        context,
        labelText: label,
      ).copyWith(
        suffixIcon: obscureText && onToggleObscure != null
            ? IconButton(
                icon: Icon(
                  obscureState! ? Icons.visibility_off : Icons.visibility,
                  color: AppColors.primaryOrange,
                ),
                onPressed: onToggleObscure,
              )
            : null,
      ),
      validator: validator,
    );
  }

  @override
  Widget build(BuildContext context) {
    final isArabic = localeProvider.locale.languageCode == 'ar';
    final colors = ThemeHelper.getColorsWithListener(context);

    return Container(
      color: colors.backgroundPrimary,
      padding: EdgeInsets.only(
        top: 20,
        left: 20,
        right: 20,
        bottom: MediaQuery.of(context).viewInsets.bottom + 20,
      ),
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    AppLocalizations.tr(context, 'edit_profile'),
                    style: ThemeHelper.getSectionTitleStyle(context),
                  ),
                  IconButton(
                    icon: Icon(Icons.close, color: colors.textPrimary),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const SizedBox(height: Constants.mediumSpacing),
              _buildTextField(
                context: context,
                controller: _nameController,
                label: AppLocalizations.tr(context, 'full_name'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppLocalizations.tr(context, 'enter_name');
                  }
                  if (value.length < 3) {
                    return AppLocalizations.tr(context, 'name_too_short');
                  }
                  return null;
                },
                isArabic: isArabic,
              ),
              const SizedBox(height: Constants.mediumSpacing),
              _buildTextField(
                context: context,
                controller: _emailController,
                label: AppLocalizations.tr(context, 'email'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppLocalizations.tr(context, 'enter_email');
                  }
                  final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
                  if (!emailRegex.hasMatch(value)) {
                    return AppLocalizations.tr(context, 'invalid_email');
                  }
                  return null;
                },
                isArabic: isArabic,
                keyboardType: TextInputType.emailAddress,
              ),
              const SizedBox(height: Constants.mediumSpacing),
              _buildTextField(
                context: context,
                controller: _phoneController,
                label: AppLocalizations.tr(context, 'phone'),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final phoneRegex = RegExp(r'^\+?\d{8,}$');
                    if (!phoneRegex
                        .hasMatch(value.replaceAll(RegExp(r'\s+'), ''))) {
                      return AppLocalizations.tr(context, 'invalid_phone');
                    }
                  }
                  return null;
                },
                isArabic: isArabic,
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: Constants.mediumSpacing),
              _buildTextField(
                context: context,
                controller: _passwordController,
                label: AppLocalizations.tr(context, 'new_password'),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (value.length < 8) {
                      return AppLocalizations.tr(
                          context, 'password_min_length');
                    }
                  }
                  return null;
                },
                isArabic: isArabic,
                obscureText: true,
                obscureState: _obscurePassword,
                onToggleObscure: () =>
                    setState(() => _obscurePassword = !_obscurePassword),
              ),
              const SizedBox(height: Constants.mediumSpacing),
              _buildTextField(
                context: context,
                controller: _confirmPasswordController,
                label: AppLocalizations.tr(context, 'confirm_password'),
                validator: (value) {
                  if (_passwordController.text.isNotEmpty) {
                    if (value != _passwordController.text) {
                      return AppLocalizations.tr(
                          context, 'passwords_not_match');
                    }
                  }
                  return null;
                },
                isArabic: isArabic,
                obscureText: true,
                obscureState: _obscureConfirmPassword,
                onToggleObscure: () => setState(
                    () => _obscureConfirmPassword = !_obscureConfirmPassword),
              ),
              if (_errorMessage != null) ...[
                const SizedBox(height: Constants.mediumSpacing),
                Text(
                  _errorMessage!,
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
              ],
              const SizedBox(height: Constants.largeSpacing),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _handleSubmit,
                  style: ThemeHelper.getPrimaryButtonStyle(context),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Text(
                          AppLocalizations.tr(context, 'save_changes'),
                          style: ThemeHelper.getButtonTextStyle(context),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}