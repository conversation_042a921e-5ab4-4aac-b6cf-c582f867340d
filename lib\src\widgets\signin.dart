import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import '../widgets/locale_provider.dart';
import '../widgets/dashboard.dart';
import '../widgets/signup.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _storage = const FlutterSecureStorage();
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  String? _validateEmail(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.tr(context, 'enter_email');
    }
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
    if (!emailRegex.hasMatch(value)) {
      return AppLocalizations.tr(context, 'invalid_email');
    }
    return null;
  }

  String? _validatePassword(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.tr(context, 'enter_password');
    }
    if (value.length < 8) {
      return AppLocalizations.tr(context, 'password_min_length');
    }
    return null;
  }

  Future<void> _handleSignIn(BuildContext context) async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      try {
        final response = await http.post(
          Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/login'),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({
            'email': _emailController.text.trim(),
            'password': _passwordController.text,
            'type': 'user',
          }),
        );

        print('API Response: ${response.statusCode} - ${response.body}');

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          final token = data['data']['token']; // Corrected to access nested token

          if (token == null || token.isEmpty) {
            setState(() {
              _errorMessage = AppLocalizations.tr(context, 'no_token_received') +
                  ': ${response.body}';
            });
            return;
          }

          // Store token securely
          await _storage.write(key: 'auth_token', value: token);

          // Verify token was stored
          String? storedToken = await _storage.read(key: 'auth_token');
          if (storedToken == null || storedToken != token) {
            setState(() {
              _errorMessage = AppLocalizations.tr(context, 'token_storage_failed');
            });
            return;
          }

          // Small delay to ensure storage completion
          await Future.delayed(const Duration(milliseconds: 100));

          // Navigate to Dashboard
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const DashboardScreen()),
          );
        } else {
          setState(() {
            _errorMessage = jsonDecode(response.body)['message'] ??
                AppLocalizations.tr(context, 'login_failed') +
                    ': ${response.statusCode}';
          });
        }
      } catch (e) {
        setState(() {
          _errorMessage = AppLocalizations.tr(context, 'network_error') + ': $e';
        });
        print('Error during sign-in: $e');
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.isArabic;
    final colors = ThemeHelper.getColorsWithListener(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isTablet = screenWidth > 600;

    return Scaffold(
      backgroundColor: colors.backgroundSecondary,
      body: Stack(
        children: [
          // Background with gradient
          _buildBackgroundSection(context, isTablet),

          // Header section
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: _buildHeaderSection(isArabic, context, isTablet),
          ),

          // Bottom sheet with login form
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildBottomSheetLogin(
                isArabic, context, isTablet, screenHeight),
          ),
        ],
      ),
    );
  }

  void _showLanguageSelector(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: ThemeHelper.getColors(context).backgroundPrimary,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 50,
              height: 5,
              decoration: BoxDecoration(
                color: ThemeHelper.getColors(context)
                    .textSecondary
                    .withOpacity(0.3),
                borderRadius: BorderRadius.circular(2.5),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Text(
                    AppLocalizations.tr(context, 'select_language'),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: ThemeHelper.getColors(context).textPrimary,
                    ),
                  ),
                  const SizedBox(height: 20),
                  _buildLanguageOption(
                      context, 'العربية', 'ar', localeProvider),
                  const SizedBox(height: 12),
                  _buildLanguageOption(
                      context, 'Français', 'fr', localeProvider),
                  const SizedBox(height: 12),
                  _buildLanguageOption(
                      context, 'English', 'en', localeProvider),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageOption(BuildContext context, String language,
      String code, LocaleProvider localeProvider) {
    final isSelected = localeProvider.locale.languageCode == code;

    return InkWell(
      onTap: () {
        localeProvider.setLocale(Locale(code));
        Navigator.pop(context);
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primaryOrange.withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? AppColors.primaryOrange
                : ThemeHelper.getColors(context).textSecondary.withOpacity(0.2),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              isSelected
                  ? Icons.radio_button_checked
                  : Icons.radio_button_unchecked,
              color: isSelected
                  ? AppColors.primaryOrange
                  : ThemeHelper.getColors(context).textSecondary,
              size: 20,
            ),
            const SizedBox(width: 12),
            Text(
              language,
              style: TextStyle(
                fontSize: 16,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                color: isSelected
                    ? AppColors.primaryOrange
                    : ThemeHelper.getColors(context).textPrimary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackgroundSection(BuildContext context, bool isTablet) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryOrange,
            AppColors.primaryOrange.withOpacity(0.9),
            AppColors.primaryOrange.withOpacity(0.8),
            AppColors.primaryOrange.withOpacity(0.7),
          ],
          stops: const [0.0, 0.3, 0.7, 1.0],
        ),
      ),
      child: CustomPaint(
        painter: _WavePainter(),
      ),
    );
  }

  Widget _buildHeaderSection(
      bool isArabic, BuildContext context, bool isTablet) {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: isTablet ? 32 : 20,
          vertical: isTablet ? 40 : 30,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Language switcher
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: AppColors.pureWhite.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
                  ),
                  child: IconButton(
                    onPressed: () => _showLanguageSelector(context),
                    icon: Icon(
                      Icons.language,
                      color: AppColors.pureWhite,
                      size: isTablet ? 24 : 20,
                    ),
                    tooltip: AppLocalizations.tr(context, 'change_language'),
                  ),
                ),
              ],
            ),
            SizedBox(height: isTablet ? 40 : 30),

            // App Title
            Center(
              child: Column(
                children: [
                  Text(
                    AppLocalizations.tr(context, 'app_title'),
                    style: TextStyle(
                      fontSize: isTablet ? 32 : 28,
                      fontWeight: FontWeight.w800,
                      color: AppColors.pureWhite,
                      letterSpacing: 1.2,
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 2),
                          blurRadius: 4,
                          color: Colors.black.withOpacity(0.3),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: isTablet ? 8 : 6),
                  Text(
                    AppLocalizations.tr(context, 'app_subtitle'),
                    style: TextStyle(
                      fontSize: isTablet ? 16 : 14,
                      fontWeight: FontWeight.w400,
                      color: AppColors.pureWhite.withOpacity(0.9),
                      letterSpacing: 0.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomSheetLogin(
      bool isArabic, BuildContext context, bool isTablet, double screenHeight) {
    final colors = ThemeHelper.getColorsWithListener(context);
    final bottomSheetHeight = screenHeight * (isTablet ? 0.55 : 0.65);

    return Container(
      height: bottomSheetHeight,
      decoration: BoxDecoration(
        color: colors.backgroundPrimary,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(isTablet ? 32 : 28),
          topRight: Radius.circular(isTablet ? 32 : 28),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withOpacity(0.2),
            blurRadius: 20,
            spreadRadius: 5,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 32 : 24,
                vertical: isTablet ? 24 : 20,
              ),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildLoginHeader(isArabic, isTablet),
                    SizedBox(height: isTablet ? 32 : 28),
                    _buildEmailField(isArabic, isTablet),
                    SizedBox(height: isTablet ? 20 : 16),
                    _buildPasswordField(isArabic, isTablet),
                    if (_errorMessage != null) ...[
                      SizedBox(height: isTablet ? 16 : 12),
                      Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: Colors.red,
                          fontSize: isTablet ? 14 : 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                    SizedBox(height: isTablet ? 32 : 28),
                    _buildLoginButton(isArabic, isTablet),
                    SizedBox(height: isTablet ? 20 : 16),
                    _buildFooterLinks(isArabic, isTablet),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginHeader(bool isArabic, bool isTablet) => Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ShaderMask(
            shaderCallback: (bounds) => LinearGradient(
              colors: [
                AppColors.primaryOrange,
                AppColors.primaryOrange.withOpacity(0.8),
              ],
            ).createShader(bounds),
            child: Text(
              AppLocalizations.tr(context, 'login'),
              style: TextStyle(
                fontSize: isTablet ? 24 : 22,
                fontWeight: FontWeight.w700,
                color: AppColors.primaryOrange,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: isTablet ? 12 : 10),
          Text(
            AppLocalizations.tr(context, 'login_subtitle'),
            style: TextStyle(
              fontSize: isTablet ? 16 : 14,
              fontWeight: FontWeight.w400,
              color: ThemeHelper.getColors(context).textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );

  Widget _buildEmailField(bool isArabic, bool isTablet) => Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryOrange.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: TextFormField(
          controller: _emailController,
          textAlign: isArabic ? TextAlign.right : TextAlign.left,
          validator: (value) => _validateEmail(value, context),
          keyboardType: TextInputType.emailAddress,
          style: TextStyle(
            fontSize: isTablet ? 16 : 14,
            fontWeight: FontWeight.w400,
            color: Colors.black,
          ),
          decoration: ThemeHelper.getInputDecoration(
            context,
            labelText: AppLocalizations.tr(context, 'email'),
            hintText: '<EMAIL>',
            prefixIcon: Icon(
              Icons.email_outlined,
              color: AppColors.primaryOrange.withOpacity(0.7),
              size: isTablet ? 24 : 20,
            ),
          ).copyWith(
            contentPadding: EdgeInsets.symmetric(
              horizontal: isTablet ? 20 : 16,
              vertical: isTablet ? 18 : 16,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
              borderSide: BorderSide(
                color: AppColors.primaryOrange,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: ThemeHelper.getColors(context).surface,
          ),
        ),
      );

  Widget _buildPasswordField(bool isArabic, bool isTablet) => Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryOrange.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: TextFormField(
          controller: _passwordController,
          obscureText: true,
          textAlign: isArabic ? TextAlign.right : TextAlign.left,
          validator: (value) => _validatePassword(value, context),
          style: TextStyle(
            fontSize: isTablet ? 16 : 14,
            fontWeight: FontWeight.w400,
            color: Colors.black,
          ),
          decoration: ThemeHelper.getInputDecoration(
            context,
            labelText: AppLocalizations.tr(context, 'password'),
            hintText: '********',
            prefixIcon: Icon(
              Icons.lock_outline,
              color: AppColors.primaryOrange.withOpacity(0.7),
              size: isTablet ? 24 : 20,
            ),
          ).copyWith(
            contentPadding: EdgeInsets.symmetric(
              horizontal: isTablet ? 20 : 16,
              vertical: isTablet ? 18 : 16,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
              borderSide: BorderSide(
                color: AppColors.primaryOrange,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: ThemeHelper.getColors(context).surface,
          ),
        ),
      );

  Widget _buildLoginButton(bool isArabic, bool isTablet) => Container(
        width: double.infinity,
        height: isTablet ? 56 : 52,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [
              AppColors.primaryOrange,
              AppColors.primaryOrange.withOpacity(0.9),
            ],
          ),
          borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryOrange.withOpacity(0.4),
              blurRadius: 15,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Builder(
          builder: (context) => ElevatedButton(
            onPressed: _isLoading ? null : () => _handleSignIn(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              foregroundColor: AppColors.pureWhite,
              elevation: 0,
              shadowColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
              ),
            ),
            child: _isLoading
                ? const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.pureWhite),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        AppLocalizations.tr(context, 'login'),
                        style: TextStyle(
                          fontSize: isTablet ? 18 : 16,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      );

  Widget _buildFooterLinks(bool isArabic, bool isTablet) => Column(
        children: [
          Align(
            alignment: Alignment.center,
            child: Builder(
              builder: (context) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      AppLocalizations.tr(context, 'no_account'),
                      style: ThemeHelper.getSubtitleStyle(context).copyWith(
                        fontSize: isTablet ? 14 : 12,
                      ),
                    ),
                    TextButton(
                      onPressed: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const SignUpScreen()),
                      ),
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.symmetric(
                          horizontal: isTablet ? 8 : 6,
                          vertical: isTablet ? 4 : 2,
                        ),
                      ),
                      child: Text(
                        AppLocalizations.tr(context, 'sign_up'),
                        style: ThemeHelper.getSubtitleStyle(context).copyWith(
                          color: AppColors.primaryOrange,
                          fontSize: isTablet ? 14 : 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      );
}

class _WavePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.pureWhite.withOpacity(0.1)
      ..style = PaintingStyle.fill;

    final path = Path();

    // Create wave pattern
    path.moveTo(0, size.height * 0.7);
    path.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.6,
      size.width * 0.5,
      size.height * 0.7,
    );
    path.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.8,
      size.width,
      size.height * 0.7,
    );
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);

    // Second wave
    final paint2 = Paint()
      ..color = AppColors.pureWhite.withOpacity(0.05)
      ..style = PaintingStyle.fill;

    final path2 = Path();
    path2.moveTo(0, size.height * 0.8);
    path2.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.7,
      size.width * 0.6,
      size.height * 0.8,
    );
    path2.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.9,
      size.width,
      size.height * 0.8,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    canvas.drawPath(path2, paint2);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}