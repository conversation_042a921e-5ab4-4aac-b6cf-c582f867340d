import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_player/video_player.dart';
import 'dart:io';
import '../config/colors.dart';
import '../config/models/constants.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import 'locale_provider.dart';

class GuideScreen extends StatefulWidget {
  const GuideScreen({super.key});

  @override
  _GuideScreenState createState() => _GuideScreenState();
}

class _GuideScreenState extends State<GuideScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late VideoPlayerController _videoController;
  bool _isVideoInitialized = false;

  final Map<String, String> _tutorial = {
    'title': 'Comment utiliser la plateforme',
    'title_ar': 'كيفية استخدام المنصة',
    'title_en': 'How to use the platform', // Added English title
    'videoPath': 'assets/videos/video.mp4',
    'description':
        'Guide complet pour utiliser toutes les fonctionnalités de la plateforme',
    'description_ar': 'دليل شامل لاستخدام جميع ميزات المنصة',
    'description_en':
        'Complete guide to using all platform features', // Added English description
  };

  final List<Map<String, String>> _pdfs = [
    {
      'title_en': 'Law 340-20',
      'title_ar': 'قانون 340-20',
      'title_fr': 'Loi 340-20',
      'path': 'assets/A2020071.pdf',
      'button_en': 'Read',
      'button_ar': 'اقرا',
      'button_fr': 'Lire',
    },
    {
      'title_en': 'Law 19-15',
      'title_ar': 'قانون 19-15',
      'title_fr': 'Loi 19-15',
      'path': 'assets/A2015007.pdf',
      'button_en': 'Read',
      'button_ar': 'اقرا',
      'button_fr': 'Lire',
    },
    {
      'title_en': 'Law 90-29',
      'title_ar': 'قانون 90-29',
      'title_fr': 'Loi 90-29',
      'path': 'assets/A1990052.pdf',
      'button_en': 'Read',
      'button_ar': 'اقرا',
      'button_fr': 'Lire',
    },
    {
      'title_en': 'Law 247-24',
      'title_ar': 'قانون 24-247',
      'title_fr': 'Loi 247-24',
      'path': 'assets/A2024051.pdf',
      'button_en': 'Read',
      'button_ar': 'اقرا',
      'button_fr': 'Lire',
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _videoController = VideoPlayerController.asset(_tutorial['videoPath']!)
      ..initialize().then((_) {
        setState(() {
          _isVideoInitialized = true;
        });
      }).catchError((error) {
        print("Error initializing video: $error");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading video: $error')),
        );
      });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _videoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.isArabic;
    final colors = ThemeHelper.getColorsWithListener(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      backgroundColor: colors.backgroundPrimary,
      body: Column(
        children: [
          // Modern header without app bar
          _buildModernHeader(context, isArabic, isTablet),

          // Custom tab bar
          _buildCustomTabBar(context, isArabic, isTablet),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildModernLawsTab(
                    isArabic, localeProvider.locale.languageCode, isTablet),
                _buildModernVideoTab(
                    isArabic, localeProvider.locale.languageCode, isTablet),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernHeader(
      BuildContext context, bool isArabic, bool isTablet) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryOrange,
            AppColors.primaryOrange.withOpacity(0.8),
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? 24 : 16,
            vertical: isTablet ? 20 : 16,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Back button and title row
              Row(
                children: [
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      isArabic ? Icons.arrow_forward : Icons.arrow_back,
                      color: AppColors.pureWhite,
                      size: isTablet ? 28 : 24,
                    ),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  SizedBox(width: isTablet ? 16 : 12),
                  Expanded(
                    child: Text(
                      AppLocalizations.tr(context, 'user_guide'),
                      style: TextStyle(
                        fontSize: isTablet ? 28 : 24,
                        fontWeight: FontWeight.bold,
                        color: AppColors.pureWhite,
                      ),
                      textAlign: isArabic ? TextAlign.right : TextAlign.left,
                    ),
                  ),
                ],
              ),
              SizedBox(height: isTablet ? 12 : 8),
              // Subtitle
              Text(
                AppLocalizations.tr(context, 'guide_subtitle'),
                style: TextStyle(
                  fontSize: isTablet ? 16 : 14,
                  color: AppColors.pureWhite.withOpacity(0.9),
                  fontWeight: FontWeight.w400,
                ),
                textAlign: isArabic ? TextAlign.right : TextAlign.left,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomTabBar(
      BuildContext context, bool isArabic, bool isTablet) {
    return Container(
      margin: EdgeInsets.all(isTablet ? 20 : 16),
      decoration: BoxDecoration(
        color: ThemeHelper.getColors(context).card,
        borderRadius: BorderRadius.circular(isTablet ? 16 : 12),
        boxShadow: [
          BoxShadow(
            color: ThemeHelper.getColors(context).shadowColor.withOpacity(0.06),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.primaryOrange,
              AppColors.primaryOrange.withOpacity(0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(isTablet ? 14 : 10),
        ),
        labelColor: AppColors.pureWhite,
        unselectedLabelColor: ThemeHelper.getColors(context).textSecondary,
        labelStyle: TextStyle(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w500,
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        tabs: [
          Tab(
            height: isTablet ? 48 : 44,
            text: AppLocalizations.tr(context, 'laws_and_regulations'),
          ),
          Tab(
            height: isTablet ? 48 : 44,
            text: AppLocalizations.tr(context, 'user_guide'),
          ),
        ],
      ),
    );
  }

  Widget _buildModernLawsTab(
      bool isArabic, String languageCode, bool isTablet) {
    return ListView.builder(
      padding: EdgeInsets.all(isTablet ? 20 : 16),
      itemCount: _pdfs.length,
      itemBuilder: (context, index) {
        final pdf = _pdfs[index];
        String titleKey = 'title_en';
        String buttonKey = 'button_en';
        if (isArabic) {
          titleKey = 'title_ar';
          buttonKey = 'button_ar';
        } else if (languageCode == 'fr') {
          titleKey = 'title_fr';
          buttonKey = 'button_fr';
        }
        final title = pdf[titleKey];
        final buttonLabel = pdf[buttonKey];
        final path = pdf['path'];
        if (title == null || path == null || buttonLabel == null) {
          return const SizedBox.shrink();
        }
        return _buildModernLawCard(
            title, buttonLabel, path, isArabic, isTablet, index);
      },
    );
  }

  Widget _buildModernLawCard(String title, String buttonLabel, String path,
      bool isArabic, bool isTablet, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: isTablet ? 20 : 16),
      decoration: BoxDecoration(
        color: ThemeHelper.getColors(context).card,
        borderRadius: BorderRadius.circular(isTablet ? 16 : 12),
        boxShadow: [
          BoxShadow(
            color: ThemeHelper.getColors(context).shadowColor.withOpacity(0.06),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 20 : 16),
        child: Row(
          children: [
            // Law icon with gradient background
            Container(
              width: isTablet ? 56 : 48,
              height: isTablet ? 56 : 48,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primaryOrange.withOpacity(0.2),
                    AppColors.primaryOrange.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(isTablet ? 14 : 12),
              ),
              child: Icon(
                Icons.gavel,
                color: AppColors.primaryOrange,
                size: isTablet ? 28 : 24,
              ),
            ),
            SizedBox(width: isTablet ? 16 : 12),
            // Title and content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: isTablet ? 18 : 16,
                      fontWeight: FontWeight.w600,
                      color: ThemeHelper.getColors(context).textPrimary,
                    ),
                    textDirection:
                        isArabic ? TextDirection.rtl : TextDirection.ltr,
                  ),
                  SizedBox(height: isTablet ? 8 : 6),
                  Text(
                    AppLocalizations.tr(context, 'law_description'),
                    style: TextStyle(
                      fontSize: isTablet ? 14 : 12,
                      color: ThemeHelper.getColors(context).textSecondary,
                    ),
                    textDirection:
                        isArabic ? TextDirection.rtl : TextDirection.ltr,
                  ),
                ],
              ),
            ),
            SizedBox(width: isTablet ? 16 : 12),
            // Modern read button
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primaryOrange,
                    AppColors.primaryOrange.withOpacity(0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primaryOrange.withOpacity(0.3),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          PdfViewerPage(pdfPath: path, title: title),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: AppColors.pureWhite,
                  elevation: 0,
                  shadowColor: Colors.transparent,
                  padding: EdgeInsets.symmetric(
                    horizontal: isTablet ? 20 : 16,
                    vertical: isTablet ? 12 : 10,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.picture_as_pdf,
                      size: isTablet ? 18 : 16,
                    ),
                    SizedBox(width: isTablet ? 8 : 6),
                    Text(
                      buttonLabel,
                      style: TextStyle(
                        fontSize: isTablet ? 14 : 12,
                        fontWeight: FontWeight.w600,
                      ),
                      textDirection:
                          isArabic ? TextDirection.rtl : TextDirection.ltr,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernVideoTab(
      bool isArabic, String languageCode, bool isTablet) {
    String titleKey = 'title_en';
    String descriptionKey = 'description_en';
    if (isArabic) {
      titleKey = 'title_ar';
      descriptionKey = 'description_ar';
    } else if (languageCode == 'fr') {
      titleKey = 'title';
      descriptionKey = 'description';
    }
    final title = _tutorial[titleKey];
    final description = _tutorial[descriptionKey];

    return SingleChildScrollView(
      padding: EdgeInsets.all(isTablet ? 20 : 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Modern header card
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(isTablet ? 24 : 20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primaryOrange.withOpacity(0.1),
                  AppColors.primaryOrange.withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(isTablet ? 16 : 12),
              border: Border.all(
                color: AppColors.primaryOrange.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(isTablet ? 12 : 10),
                      decoration: BoxDecoration(
                        color: AppColors.primaryOrange.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
                      ),
                      child: Icon(
                        Icons.play_circle_filled,
                        color: AppColors.primaryOrange,
                        size: isTablet ? 28 : 24,
                      ),
                    ),
                    SizedBox(width: isTablet ? 16 : 12),
                    Expanded(
                      child: Text(
                        title ?? AppLocalizations.tr(context, 'user_guide'),
                        style: TextStyle(
                          fontSize: isTablet ? 22 : 20,
                          fontWeight: FontWeight.bold,
                          color: ThemeHelper.getColors(context).textPrimary,
                        ),
                        textDirection:
                            isArabic ? TextDirection.rtl : TextDirection.ltr,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: isTablet ? 16 : 12),
                Text(
                  description ??
                      AppLocalizations.tr(context, 'video_guide_description'),
                  style: TextStyle(
                    fontSize: isTablet ? 16 : 14,
                    color: ThemeHelper.getColors(context).textSecondary,
                    height: 1.5,
                  ),
                  textDirection:
                      isArabic ? TextDirection.rtl : TextDirection.ltr,
                ),
              ],
            ),
          ),
          SizedBox(height: isTablet ? 24 : 20),

          // Modern video player container
          Container(
            decoration: BoxDecoration(
              color: ThemeHelper.getColors(context).card,
              borderRadius: BorderRadius.circular(isTablet ? 16 : 12),
              boxShadow: [
                BoxShadow(
                  color: ThemeHelper.getColors(context)
                      .shadowColor
                      .withOpacity(0.06),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(isTablet ? 16 : 12),
              child: _isVideoInitialized
                  ? AspectRatio(
                      aspectRatio: _videoController.value.aspectRatio,
                      child: Stack(
                        alignment: Alignment.bottomCenter,
                        children: [
                          VideoPlayer(_videoController),
                          // Custom video controls overlay
                          Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.transparent,
                                  Colors.black.withOpacity(0.3),
                                ],
                              ),
                            ),
                            child: VideoProgressIndicator(
                              _videoController,
                              allowScrubbing: true,
                              colors: VideoProgressColors(
                                playedColor: AppColors.primaryOrange,
                                bufferedColor:
                                    AppColors.pureWhite.withOpacity(0.5),
                                backgroundColor:
                                    AppColors.pureWhite.withOpacity(0.2),
                              ),
                            ),
                          ),
                          // Play/pause button
                          Align(
                            alignment: Alignment.center,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.5),
                                borderRadius:
                                    BorderRadius.circular(isTablet ? 40 : 35),
                              ),
                              child: IconButton(
                                icon: Icon(
                                  _videoController.value.isPlaying
                                      ? Icons.pause
                                      : Icons.play_arrow,
                                  color: AppColors.pureWhite,
                                  size: isTablet ? 48 : 40,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _videoController.value.isPlaying
                                        ? _videoController.pause()
                                        : _videoController.play();
                                  });
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  : Container(
                      height: isTablet ? 300 : 250,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.primaryOrange,
                              ),
                            ),
                            SizedBox(height: isTablet ? 16 : 12),
                            Text(
                              AppLocalizations.tr(context, 'loading_video'),
                              style: TextStyle(
                                fontSize: isTablet ? 16 : 14,
                                color: ThemeHelper.getColors(context)
                                    .textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }
}

class PdfViewerPage extends StatefulWidget {
  final String pdfPath;
  final String title;

  const PdfViewerPage({Key? key, required this.pdfPath, required this.title})
      : super(key: key);

  @override
  _PdfViewerPageState createState() => _PdfViewerPageState();
}

class _PdfViewerPageState extends State<PdfViewerPage> {
  String? localPath;

  Future<String?> _loadPdf() async {
    try {
      print("Tentative de chargement du PDF depuis: ${widget.pdfPath}");
      final assetBundle = DefaultAssetBundle.of(context);
      final byteData = await assetBundle.load(widget.pdfPath);
      if (byteData == null) {
        print("Échec: byteData est null pour ${widget.pdfPath}");
        throw Exception("Fichier PDF introuvable : ${widget.pdfPath}");
      }

      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/${widget.pdfPath.split('/').last}');
      await file.writeAsBytes(byteData.buffer.asUint8List());
      if (!await file.exists()) {
        print("Échec: Fichier temporaire non créé à ${file.path}");
        throw Exception("Fichier temporaire non créé : ${file.path}");
      }

      print("Succès: PDF chargé, localPath: ${file.path}");
      return file.path;
    } catch (e) {
      print("Erreur lors du chargement du PDF: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur de chargement du PDF : $e')),
      );
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.isArabic;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.title,
          style: ThemeHelper.getTitleStyle(context),
          textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
        ),
        backgroundColor: AppColors.primaryOrange,
        foregroundColor: AppColors.pureWhite,
      ),
      body: FutureBuilder<String?>(
        future: _loadPdf(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError || snapshot.data == null) {
            print("Erreur ou données nulles: ${snapshot.error}");
            return Center(
                child: Text(
                    'Erreur de chargement: ${snapshot.error ?? "Fichier non trouvé"}'));
          }
          return PDFView(
            filePath: snapshot.data!,
            enableSwipe: true,
            swipeHorizontal: false,
            autoSpacing: true,
            pageFling: true,
            onError: (error) {
              print("Erreur d'affichage du PDF: $error");
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Erreur d\'affichage du PDF : $error')),
              );
            },
          );
        },
      ),
    );
  }
}
